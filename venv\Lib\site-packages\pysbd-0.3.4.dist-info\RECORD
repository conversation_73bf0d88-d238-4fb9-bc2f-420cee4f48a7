benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
benchmarks/__pycache__/__init__.cpython-311.pyc,,
benchmarks/__pycache__/benchmark_sbd_tools.cpython-311.pyc,,
benchmarks/__pycache__/bigtext_speed_benchmark.cpython-311.pyc,,
benchmarks/__pycache__/english_golden_rules.cpython-311.pyc,,
benchmarks/__pycache__/genia.cpython-311.pyc,,
benchmarks/__pycache__/genia_benchmark.cpython-311.pyc,,
benchmarks/__pycache__/universal_dependency_sbd.cpython-311.pyc,,
benchmarks/benchmark_sbd_tools.py,sha256=uNFqjmLFdWi-dFO13qzk20azf2BpPAOlDv_tJlnIAUk,2336
benchmarks/bigtext_speed_benchmark.py,sha256=BYSdQCqzpHZMhdPIE-j-i8lNq86JOlF5zssYBaCyFoc,2167
benchmarks/english_golden_rules.py,sha256=3sVCjREaSsC8g-qgetE91-60RRkDGz0EpCW4TI21pMU,9352
benchmarks/genia.py,sha256=BqcQOuVXpQZUUegFldZIkDX0K5IsQ7mrYgGgMDJz7dI,2106
benchmarks/genia_benchmark.py,sha256=ooBhV9q4I5HDRXZmYRJsWMJkQAcrfwG4woVg6EfdgWI,2713
benchmarks/universal_dependency_sbd.py,sha256=DkY_mOgxkjzoNGxvugV1FrAgBIrmiqwHTHPt-Vm5FKk,2100
examples/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
examples/__pycache__/__init__.cpython-311.pyc,,
examples/__pycache__/pysbd_as_spacy_component.cpython-311.pyc,,
examples/__pycache__/test_timing_script.cpython-311.pyc,,
examples/pysbd_as_spacy_component.py,sha256=jzoP-OiMPPEUYeypfb9WblnFopdKZcndfihw6CplswU,948
examples/test_timing_script.py,sha256=at8vHlrgDa1w6U-h3JGk2nWAzPbjIOgXmI_envvRpbo,34460
pysbd-0.3.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pysbd-0.3.4.dist-info/LICENSE,sha256=kLP6thC-pIqHHaADi8vG_Sko4wnCaJ81uEJ-MkxuyD4,1072
pysbd-0.3.4.dist-info/METADATA,sha256=kVj1wz8-0cWg6hCe0vcnb5ozTUmkMcmY-JBMdTTcXd4,6066
pysbd-0.3.4.dist-info/RECORD,,
pysbd-0.3.4.dist-info/WHEEL,sha256=EVRjI69F5qVjm_YgqcTXPnTAv3BfSUr0WVAHuSP3Xoo,92
pysbd-0.3.4.dist-info/entry_points.txt,sha256=p2uj-nqsPLNK6PKKguGnI-o8xdkziOgHY4-8gkraxY0,52
pysbd-0.3.4.dist-info/top_level.txt,sha256=QIwvJoyHTeG8DTPudwH1bOL6k3M0zUY-HSj93HvOfpM,26
pysbd/__init__.py,sha256=eiaanYu7tFbIAUlTDZYPikTVonQ4BRpFThRJ-2L3foo,64
pysbd/__pycache__/__init__.cpython-311.pyc,,
pysbd/__pycache__/abbreviation_replacer.cpython-311.pyc,,
pysbd/__pycache__/about.cpython-311.pyc,,
pysbd/__pycache__/between_punctuation.cpython-311.pyc,,
pysbd/__pycache__/cleaner.cpython-311.pyc,,
pysbd/__pycache__/exclamation_words.cpython-311.pyc,,
pysbd/__pycache__/languages.cpython-311.pyc,,
pysbd/__pycache__/lists_item_replacer.cpython-311.pyc,,
pysbd/__pycache__/processor.cpython-311.pyc,,
pysbd/__pycache__/punctuation_replacer.cpython-311.pyc,,
pysbd/__pycache__/segmenter.cpython-311.pyc,,
pysbd/__pycache__/utils.cpython-311.pyc,,
pysbd/abbreviation_replacer.py,sha256=Pnp-WoUc6GepYIPtvrM6ppRoImP5CcabD_aQcwawekY,4042
pysbd/about.py,sha256=wT9HDSikLTE6ux5fmxP3I5Qo9z5d-xrszCfj17UAJh4,439
pysbd/between_punctuation.py,sha256=w3M4U7kddAAe4yIzSWNjMTGVOBeUAib7rjFnUZYsFtE,3945
pysbd/clean/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pysbd/clean/__pycache__/__init__.cpython-311.pyc,,
pysbd/clean/__pycache__/rules.cpython-311.pyc,,
pysbd/clean/rules.py,sha256=nreKuoEUxmGq7C5SwZUGO5ZrzhW_R6ao0cE3O6UvaUU,2753
pysbd/cleaner.py,sha256=-VEV_ZwDpPrI041fhPqo-C2DIzYAAEnKrXwxMKfz7L8,4436
pysbd/exclamation_words.py,sha256=zrvYF6Hk4NUQJVRF6zt91LYTNUOj1chwlmu3jXT70RY,628
pysbd/lang/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pysbd/lang/__pycache__/__init__.cpython-311.pyc,,
pysbd/lang/__pycache__/amharic.cpython-311.pyc,,
pysbd/lang/__pycache__/arabic.cpython-311.pyc,,
pysbd/lang/__pycache__/armenian.cpython-311.pyc,,
pysbd/lang/__pycache__/bulgarian.cpython-311.pyc,,
pysbd/lang/__pycache__/burmese.cpython-311.pyc,,
pysbd/lang/__pycache__/chinese.cpython-311.pyc,,
pysbd/lang/__pycache__/danish.cpython-311.pyc,,
pysbd/lang/__pycache__/deutsch.cpython-311.pyc,,
pysbd/lang/__pycache__/dutch.cpython-311.pyc,,
pysbd/lang/__pycache__/english.cpython-311.pyc,,
pysbd/lang/__pycache__/french.cpython-311.pyc,,
pysbd/lang/__pycache__/greek.cpython-311.pyc,,
pysbd/lang/__pycache__/hindi.cpython-311.pyc,,
pysbd/lang/__pycache__/italian.cpython-311.pyc,,
pysbd/lang/__pycache__/japanese.cpython-311.pyc,,
pysbd/lang/__pycache__/kazakh.cpython-311.pyc,,
pysbd/lang/__pycache__/marathi.cpython-311.pyc,,
pysbd/lang/__pycache__/persian.cpython-311.pyc,,
pysbd/lang/__pycache__/polish.cpython-311.pyc,,
pysbd/lang/__pycache__/russian.cpython-311.pyc,,
pysbd/lang/__pycache__/slovak.cpython-311.pyc,,
pysbd/lang/__pycache__/spanish.cpython-311.pyc,,
pysbd/lang/__pycache__/urdu.cpython-311.pyc,,
pysbd/lang/amharic.py,sha256=rQRcy9NFzK14_FlN3VHZ01EwvF8AQEueZRmUIbbOH04,371
pysbd/lang/arabic.py,sha256=mmtaYOc7Nj6tTvaMBJOal1TkQ6tj7FPZWMh9oQj4V3I,1273
pysbd/lang/armenian.py,sha256=Z7rmb-e7Ocy--UV8sljP3DOIaB_mti9kBLga3WcT2Ls,361
pysbd/lang/bulgarian.py,sha256=mVBfP9mX49ytr_t1UOsRyctHQnVXhDi4X2sjoII2bQk,1336
pysbd/lang/burmese.py,sha256=9Unk59Zu4WrwWNgJwrX4-0yF5Aukmek6aQFmIL68nC0,371
pysbd/lang/chinese.py,sha256=Fv2RkbmCVr1vx1vSCEzpZpDxg7YtlACR_mVpuveHFq0,1388
pysbd/lang/common/__init__.py,sha256=pAIrpkLU2H4G4k5yn_20DZ1Viq5Xnn8d9PvOyAbP5qE,86
pysbd/lang/common/__pycache__/__init__.cpython-311.pyc,,
pysbd/lang/common/__pycache__/common.cpython-311.pyc,,
pysbd/lang/common/__pycache__/standard.cpython-311.pyc,,
pysbd/lang/common/common.py,sha256=iyEd9ASjscbqpZjZKvKyGLap9-ref7WU1zipDKA_TOY,3851
pysbd/lang/common/standard.py,sha256=CXE2bpUAjXsg5Ws1-_agqeFSBwvYIQUM7PQG9JR3jdY,6551
pysbd/lang/danish.py,sha256=nINKdSy7I1ZbLFMbhU6uwVTeM-zTohBhwwh8dqQK-WQ,5161
pysbd/lang/deutsch.py,sha256=JDLTisce1Jh4JD5C0jBAKQovDmJ1cwvTj1Y7w4HBAsU,5187
pysbd/lang/dutch.py,sha256=R7ZaHC7cxMT8SdNYhafenJ5oHXC-akKOA_ytT1tXrCE,16963
pysbd/lang/english.py,sha256=OM1dBzifvMEE7fYPMplbBC2qyWE6VP0hVLsxWewj_Ko,403
pysbd/lang/french.py,sha256=w3MZAExhnLimH5l8xoG3ah1fkmK0guw7gdgSKEOOoUw,1182
pysbd/lang/greek.py,sha256=YrxP-IGO5qqqvqLZUJ1CPEdhJkEAes4SFUddUQjnyKg,362
pysbd/lang/hindi.py,sha256=UydJ9Qm8MK7CdX2fmmSBTpmNj5biT7-gtB1m0Js3T1M,371
pysbd/lang/italian.py,sha256=cH3Ld5G72PbVtCiUtPX9IZ4y9PgjgwV9jBPzg5FL0KI,18998
pysbd/lang/japanese.py,sha256=RRpj9u6xANWkDZEwF-MIGeQM-CGFFg47tVRAIr7ee80,1811
pysbd/lang/kazakh.py,sha256=DDNviQYkQrHyaWNPO8Bq2bEAHi6GDNGi6_H0i1wnSjk,5220
pysbd/lang/marathi.py,sha256=DsB-mUgibxffdd4nC_D_x8adqZykH7VJqaJsFJ6TV7c,436
pysbd/lang/persian.py,sha256=WN_9PDUORBGbu2ntoW8TQ6UGwhXZRrNi7UPgDR0AS1g,897
pysbd/lang/polish.py,sha256=6XwmOuA4ikRLl5hqXEdMiUqbnpbwX7zmcb6ZDC3-dU4,1534
pysbd/lang/russian.py,sha256=31ke7owOGo6j72plb2mBZRCIteNOKffCJnZrMCzxgS8,1486
pysbd/lang/slovak.py,sha256=LnF802SikSYnnesOBFRLKOkX3Oq7TqpN5MEEBKepUKg,6720
pysbd/lang/spanish.py,sha256=HtoDZE1hlMwor1MGzgQ210ER_I0AGinWxzZtvvXUqjk,3089
pysbd/lang/urdu.py,sha256=Ke5hZK6ZMuRwvG2hIflrGfIi9wXh6R2UVD0FvczKESU,364
pysbd/languages.py,sha256=xuzAuF-eoosvPP0K0nTm3dO2LAjCLl63I54F33ueThk,1714
pysbd/lists_item_replacer.py,sha256=Xx6c5_L41xXH01c_AhASDh6trk9Sy9-zIoDlLdrXs5I,10431
pysbd/processor.py,sha256=WUsUZ08buk509X_QFVdYoL7OAzq7xRbsKyGi1xhzgi4,7836
pysbd/punctuation_replacer.py,sha256=UjhSe2-R-m30Sxwq9NInovy1lxSd3ecQBLUKxvQXv10,1483
pysbd/segmenter.py,sha256=r5Kytuo7aFXIAZZ4LKl0ExPJ1WQON2xIU0Sa_tOeDJI,4076
pysbd/utils.py,sha256=1uZp8uOkKen9L-ptasB2pUsSB3VrB_d_fwlWVLAKcHs,2390
