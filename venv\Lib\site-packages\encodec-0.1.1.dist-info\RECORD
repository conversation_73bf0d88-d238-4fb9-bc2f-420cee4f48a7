../../Scripts/encodec.exe,sha256=co9gRZ5eK3NgdWJp3jz45Hkq3ZVzcNCvOfRQsL_6gQs,108427
encodec-0.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
encodec-0.1.1.dist-info/METADATA,sha256=71rPiMGUWbzYjzgX-iJCDDpiK7-qVXJGlwCGo4pPuBg,7738
encodec-0.1.1.dist-info/RECORD,,
encodec-0.1.1.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
encodec-0.1.1.dist-info/entry_points.txt,sha256=d9NPOIbDU6BnaCtJfEoFLVgMvRVpFbEf-MmJhOoSvgk,50
encodec-0.1.1.dist-info/licenses/LICENSE,sha256=p5UMD-2I6aF3YX44NuncCuT0qW5ZsVw3Bt44rOFxrKQ,19333
encodec-0.1.1.dist-info/top_level.txt,sha256=kfaW-Fjm0oqdg14hmHYRh1Coo5H-3m80lqfvS64MfIM,8
encodec/__init__.py,sha256=uS_8bG_049gnmaqZQJPNQgtoTBXgTAtzYfnykvWji6w,347
encodec/__main__.py,sha256=PCdYgxeCeDKYXErLHq8KkRix9zfq5B_MoqjOoLytG3o,4493
encodec/__pycache__/__init__.cpython-311.pyc,,
encodec/__pycache__/__main__.cpython-311.pyc,,
encodec/__pycache__/binary.cpython-311.pyc,,
encodec/__pycache__/compress.cpython-311.pyc,,
encodec/__pycache__/distrib.cpython-311.pyc,,
encodec/__pycache__/model.cpython-311.pyc,,
encodec/__pycache__/msstftd.cpython-311.pyc,,
encodec/__pycache__/utils.cpython-311.pyc,,
encodec/binary.py,sha256=LugKRbRvJ4B8py9k7ZUQlCTPh-3KNI4MYPwCJWp62nA,5266
encodec/compress.py,sha256=3B3tIvEUw1OFpJzY3H3eD_oovmuFjg8w3jAGyP_8k5M,8316
encodec/distrib.py,sha256=ka8Yj_MaQitiYTETzcsD7uiCoRk_Evu7_8O0OQ9jQ10,2159
encodec/model.py,sha256=wFiRYhlRZs3260h5bgCAc8SvWQwje2IqgR598iNN75M,12822
encodec/modules/__init__.py,sha256=30ZLBRavd64_XKeUQuCdD4NZLWCB4ITxIqhmWB6EsKg,525
encodec/modules/__pycache__/__init__.cpython-311.pyc,,
encodec/modules/__pycache__/conv.cpython-311.pyc,,
encodec/modules/__pycache__/lstm.cpython-311.pyc,,
encodec/modules/__pycache__/norm.cpython-311.pyc,,
encodec/modules/__pycache__/seanet.cpython-311.pyc,,
encodec/modules/__pycache__/transformer.cpython-311.pyc,,
encodec/modules/conv.py,sha256=NBYCgZ3wGoASNwG10xSF5AGCrUnQF6rGXqvGdekwqrU,10731
encodec/modules/lstm.py,sha256=U9BRHy6xfyo-fb8PO5aeBak5KqD5hqPqkYhA2liq_8Q,782
encodec/modules/norm.py,sha256=I9VyfDYr_NQ-6LopclMba2OZGpdMEakVaxeRNM4sh9M,843
encodec/modules/seanet.py,sha256=O19FaPYQUGseRDgp4wkpGRCW94vWSuXmj0GdY-DxX98,12066
encodec/modules/transformer.py,sha256=LxXK-ug91AyExXbHXppLb3Xw5-8QH-Es1l9mHalEuD0,4598
encodec/msstftd.py,sha256=qXd_2XfjXixiO5sQm7FLyflFlreZX5hf0T0-pi388EI,6921
encodec/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
encodec/quantization/__init__.py,sha256=NMgGvByvyLg1kmtvZFC-52n5XrRnzxwZtEJ-ndflW7w,271
encodec/quantization/__pycache__/__init__.cpython-311.pyc,,
encodec/quantization/__pycache__/ac.cpython-311.pyc,,
encodec/quantization/__pycache__/core_vq.cpython-311.pyc,,
encodec/quantization/__pycache__/vq.cpython-311.pyc,,
encodec/quantization/ac.py,sha256=e5vlb9GrnSzSBWfB_elRToYTW7xKCJG5nxaCKCxHqVc,12953
encodec/quantization/core_vq.py,sha256=dYMjYqdUqBuo9YPx4azoIuygTsdMx0ZFZcE5MBmnobI,13215
encodec/quantization/vq.py,sha256=V9SRPeMLJt_LrniIzZhZmun94ANfY-yc_ZheScaxjVc,4471
encodec/utils.py,sha256=1awFV2YqHGqPQ38jdqDvaISF3Sa5v2bcKwAc-SnIvwc,3744
