../../Scripts/gruut.exe,sha256=7RC88x8liAUHOaObcQkTeCRi2IlSNB3oOLgCRpHi6bk,108425
gruut-2.2.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gruut-2.2.3.dist-info/METADATA,sha256=nXjKw1ai2DpFojK0rW4OjCsiaj-SgLYz2ZhaxYzLmxE,19188
gruut-2.2.3.dist-info/RECORD,,
gruut-2.2.3.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
gruut-2.2.3.dist-info/entry_points.txt,sha256=bwnXR7UqFugroEXBlWt__twS2Obqo4F8AYLosm3ky4Y,46
gruut-2.2.3.dist-info/licenses/LICENSE,sha256=xRAfhRqaYjqWosWFGJ2l0ZyPBiuuoafRAtGw2okK6oA,1071
gruut-2.2.3.dist-info/top_level.txt,sha256=_fNltiZzbJu-PsM452wDwRe58_clA2Z8cbsGe1wpQ30,12
gruut/VERSION,sha256=TWbOQ0xf-4DyhiJ-kY934UqbyA57zERTU8AtSEwztpk,6
gruut/__init__.py,sha256=EhBJtz9owGIIfvZ7GpwhlGgvgNeWxIKc1t5uiKcs8Qg,2996
gruut/__main__.py,sha256=3ePd9_eLqpT6dzkCcge0pG5fydr93oqfTRKrpZOy45w,9052
gruut/__pycache__/__init__.cpython-311.pyc,,
gruut/__pycache__/__main__.cpython-311.pyc,,
gruut/__pycache__/const.cpython-311.pyc,,
gruut/__pycache__/corpus2db.cpython-311.pyc,,
gruut/__pycache__/g2p.cpython-311.pyc,,
gruut/__pycache__/g2p_phonetisaurus.cpython-311.pyc,,
gruut/__pycache__/lang.cpython-311.pyc,,
gruut/__pycache__/lexicon2db.cpython-311.pyc,,
gruut/__pycache__/phonemize.cpython-311.pyc,,
gruut/__pycache__/pos.cpython-311.pyc,,
gruut/__pycache__/resources.cpython-311.pyc,,
gruut/__pycache__/text_processor.cpython-311.pyc,,
gruut/__pycache__/utils.cpython-311.pyc,,
gruut/const.py,sha256=EDHI5KPf6vFJwsHeM7OJRrOBuS2gdrkqfrbr4epqBJc,24985
gruut/corpus2db.py,sha256=HHjCkWyHjjDIkpspDKwCrn5vCRrw12c2xBPAEirQXQ4,2235
gruut/g2p.py,sha256=pkOMl0093dSLmxqVlrchPxQQ2bJvmJmLZHhLSszZfIw,13397
gruut/g2p_phonetisaurus.py,sha256=C8UIq_L-P0juD41HmKl7jDgz4DleqfTBtYNGy7MpmJg,15357
gruut/lang.py,sha256=PbVJlIvzBHNBEL-EaymmjtNRT9JdVziXsEtAC1L2hVE,30776
gruut/lexicon2db.py,sha256=G_A78mahIiFEvvbMOIHgtsVYFR9apVl42gaKVowAc74,3562
gruut/phonemize.py,sha256=-C7B1ITaafHBbZ0WP2tbK_FGOge3o8w2GMgBcVWpS28,3604
gruut/pos.py,sha256=ceLcYAVs7RPobtREMo8kg3YH5bQWei3yylNboicw3Co,13148
gruut/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gruut/resources.py,sha256=itkbr9sMGNoBapf7dc6CGVZRvVmcILYE1OuMyTXCl78,377
gruut/text_processor.py,sha256=9cgRhsCikkwT8uN4DD-ya6B7lU_IJAHPywRrMEX-7Es,89503
gruut/utils.py,sha256=GC4WrgBb5vCM0Rkqye2EEWb0EhXDhBbWhPxSv3S6QFc,10408
tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tests/__pycache__/__init__.cpython-311.pyc,,
tests/__pycache__/test_en.cpython-311.pyc,,
tests/__pycache__/test_fr.cpython-311.pyc,,
tests/__pycache__/test_g2p.cpython-311.pyc,,
tests/__pycache__/test_golden_rules.cpython-311.pyc,,
tests/__pycache__/test_pos.cpython-311.pyc,,
tests/__pycache__/test_sqlite_phonemizer.cpython-311.pyc,,
tests/__pycache__/test_ssml.cpython-311.pyc,,
tests/__pycache__/test_text_processor.cpython-311.pyc,,
tests/test_en.py,sha256=8vrO8VKfsGZ9bD44jPftxiqj__RsqS7q831Fm5li-6E,3950
tests/test_fr.py,sha256=Ju86QdAj6bWYNLOEf7Qs5YxBEcJjzaWH7sNWtwEAa0Q,2599
tests/test_g2p.py,sha256=z_4KpY9PhI_898IcQ0ovGymez7plktkn0PpTVkJNQS8,1297
tests/test_golden_rules.py,sha256=rBkcKfc50tf4lDR_NTQefzuenbP3vGb7wzCOjd7NPtU,12115
tests/test_pos.py,sha256=fmuT7bB3zhKEpqafjFViRPTVrdSrsdXh9UP_TtxWHCA,2685
tests/test_sqlite_phonemizer.py,sha256=guHi0Pv1ReQP22n0owKpvSLJF4_Zw2IQSvzsfwM22OA,9562
tests/test_ssml.py,sha256=XF8v3yFkUIJTotlvtpf_gu3Vacg3et_zLuI-0BG4D-o,12599
tests/test_text_processor.py,sha256=IDL87KbH1wEcHvNlGsQnmlZ_cKA9dEPYXtQnRZVsKpI,40309
